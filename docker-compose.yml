version: "3.8"

services:
  neo4j:
    container_name: neo4j
    restart: always
    ports:
      - "${NEO4J_HTTP_PORT:-7474}:7474"  # HTTP
      - "${NEO4J_HTTPS_PORT:-7473}:7473"  # HTTPS
      - "${NEO4J_BOLT_PORT:-7687}:7687"  # Bolt
    environment:
      - NEO4J_AUTH=${NEO4J_USERNAME}/${NEO4J_PASSWORD}
      - NEO4J_ACCEPT_LICENSE_AGREEMENT=yes
      - NEO4J_server_memory_pagecache_size=${NEO4J_PAGECACHE_SIZE}
      - NEO4J_server_memory_heap_initial__size=${NEO4J_HEAP_INITIAL_SIZE}
      - NEO4J_server_memory_heap_max__size=${NEO4J_HEAP_MAX_SIZE}
      - NEO4J_server_https_enabled=${NEO4J_HTTPS_ENABLED}
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*,algo.*
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
      - ./certs:/var/lib/neo4j/certificates
    networks:
      - neo4j_network
    healthcheck:
      test: ["CMD-SHELL", "cypher-shell -u ${NEO4J_USERNAME} -p ${NEO4J_PASSWORD} 'RETURN 1'"]
      interval: 30s
      timeout: 10s
      retries: 5
    image: neo4j:2025.06.2-ubi9

volumes:
  neo4j_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /aidata/stacks/neo4j/data
  neo4j_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /aidata/stacks/neo4j/logs
  neo4j_import:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /aidata/stacks/neo4j/import
  neo4j_plugins:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /aidata/stacks/neo4j/plugins
  neo4j_ops_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /aidata/stacks/neo4j/ops_data

networks:
  neo4j_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
