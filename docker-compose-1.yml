version: '3.8'

services:
  neo4j:
    container_name: neo4j
    restart: always
    ports:
      - "7474:7474"  # HTTP
      - "7473:7473"  # HTTPS
      - "7687:7687"  # Bolt
    environment:
      - NEO4J_AUTH=neo4j/lesso2025
      - NEO4J_ACCEPT_LICENSE_AGREEMENT=yes
      - NEO4J_dbms_memory_pagecache_size=1G
      - NEO4J_dbms_memory_heap_initial__size=1G
      - NEO4J_dbms_memory_heap_max__size=1G
      - NEO4J_dbms_connector_https_enabled=true
      - NEO4J_dbms_connector_https_address=0.0.0.0:7473
      - NEO4J_dbms_directories_certificates=/var/lib/neo4j/certificates
      - NEO4J_dbms_backup_enabled=true
      - NEO4J_dbms_backup_address=0.0.0.0:6362
      - NEO4J_dbms_logs_debug_level=INFO
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*,algo.*
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
      - ./certs:/var/lib/neo4j/certificates:ro
    networks:
      - neo4j_network
    healthcheck:
      test: ["CMD-SHELL", "cypher-shell -u neo4j -p lesso2025 'RETURN 1'"]
      interval: 30s
      timeout: 10s
      retries: 5
    image: neo4j:2025.06.2-ubi9

  neo4j-ops:
    container_name: neo4j-ops
    hostname: neo4j-ops-server
    image: docker.io/neo4j/neo4j-ops-manager-server:1.13
    ports:
      - "8080:8080"  # Web UI
      - "9090:9090"  # gRPC
    environment:
      SPRING_NEO4J_URI: bolt://neo4j:7687
      SPRING_NEO4J_AUTHENTICATION_USERNAME: neo4j
      SPRING_NEO4J_AUTHENTICATION_PASSWORD: lesso2025
      SERVER_SSL_KEY_STORE_TYPE: PKCS12
      SERVER_SSL_KEY_STORE: /app/certs/neo4j-ops.pfx
      SERVER_SSL_KEY_STORE_PASSWORD: ops2025
      GRPC_SERVER_SECURITY_KEY_STORE_TYPE: PKCS12
      GRPC_SERVER_SECURITY_KEY_STORE: /app/certs/neo4j-ops.pfx
      GRPC_SERVER_SECURITY_KEY_STORE_PASSWORD: ops2025
      CORS_ALLOWEDHEADERS: "*"
      CORS_ALLOWEDORIGINS: "http://localhost:*,https://localhost:*"
      SPRING_PROFILES_ACTIVE: production
      LOGGING_LEVEL_ROOT: INFO
      SERVER_PORT: 8080
      GRPC_SERVER_PORT: 9090
    volumes:
      - ./certs:/app/certs:ro
      - neo4j_ops_data:/app/data
    networks:
      - neo4j_network
    depends_on:
      neo4j:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/actuator/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

volumes:
  neo4j_data:
    driver: local
  neo4j_logs:
    driver: local
  neo4j_import:
    driver: local
  neo4j_plugins:
    driver: local
  neo4j_ops_data:
    driver: local

networks:
  neo4j_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16